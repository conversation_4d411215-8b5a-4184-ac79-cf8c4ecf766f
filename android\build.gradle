buildscript {
    ext.kotlin_version = '1.9.20'
    repositories {
        google()
        mavenCentral()
    }

    dependencies {
        classpath 'com.android.tools.build:gradle:8.2.1'

//        classpath 'com.android.tools.build:gradle:7.3.1'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.3.15'
    }
}

allprojects {
    repositories {
        google()
        mavenCentral()
    }
}

// Configure namespace for plugins that don't have it
subprojects {
    afterEvaluate { project ->
        if (project.hasProperty('android')) {
            if (project.name == 'deepar_flutter') {
                project.android.namespace = 'ai.deepar.ar'
            }
            if (project.name == 'video_thumbnail') {
                project.android.namespace = 'xyz.justsoft.video_thumbnail'
            }
        }
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
